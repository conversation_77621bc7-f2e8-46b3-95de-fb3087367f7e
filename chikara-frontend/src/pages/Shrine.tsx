import { DisplayAvatar } from "@/components/DisplayAvatar";
import { CountdownTimer } from "@/components/Layout/CountdownTimer";
import { getNextMidnightDate, formatTimeToNow } from "@/helpers/dateHelpers";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { useState, type FormEvent } from "react";
import toast from "react-hot-toast";
import { Link } from "react-router-dom";
import { Gift, Users, Coins, Clock, Sparkles, TrendingUp, Star, Heart, Lock, CheckCircle } from "lucide-react";
import { useGetShrineGoal, type ShrineGoal } from "@/features/shrine/api/useGetShrineGoal";
import { useGetShrineDonations, type ShrineDonation } from "@/features/shrine/api/useGetShrineDonations";
import { useDonateToShrine } from "@/features/shrine/api/useDonateToShrine";
import { useGetUserDonationStatus } from "@/features/shrine/api/useGetUserDonationStatus";
import { formatCurrency, getCurrencySymbol } from "@/utils/currencyHelpers";
import type { User } from "@/types/user";
import type { AppRouterClient } from "@/lib/orpc";

interface DailyGoalProps {
    shrineGoal: ShrineGoal | undefined;
    userDonationStatus: Awaited<ReturnType<AppRouterClient["shrine"]["getUserDonationStatus"]>> | undefined;
}

interface DonationFormProps {
    shrineMinDonation: number;
    currentUser: User | undefined;
    userDonationStatus: Awaited<ReturnType<AppRouterClient["shrine"]["getUserDonationStatus"]>> | undefined;
}

interface DonationTableProps {
    recentDonations: ShrineDonation[] | undefined;
}

// Main component for the Shrine page
function Shrine() {
    const { isLoading, data: shrineGoal } = useGetShrineGoal();
    const { data: currentUser } = useFetchCurrentUser();
    const { data: recentDonations } = useGetShrineDonations();
    const { data: userDonationStatus } = useGetUserDonationStatus();
    const { ROGUELIKE_DISABLED, SHRINE_MINIMUM_DONATION } = useGameConfig();

    // Display loading or disabled state
    if (isLoading)
        return (
            <div className="min-h-screen bg-gray-950 flex items-center justify-center">
                <div className="text-center text-gray-400">
                    <Sparkles className="w-12 h-12 mx-auto mb-4 animate-pulse text-violet-400" />
                    <p className="text-lg">Loading Shrine...</p>
                </div>
            </div>
        );

    if (ROGUELIKE_DISABLED && currentUser?.userType !== "admin") {
        return (
            <div className="min-h-screen bg-gray-950 flex items-center justify-center">
                <div className="bg-gray-900/50 border border-gray-800 rounded-xl p-8 max-w-md mx-auto text-center">
                    <Heart className="w-16 h-16 mx-auto mb-4 text-gray-600" />
                    <h2 className="text-2xl font-bold text-white mb-2">The Shrine is Currently Disabled</h2>
                    <p className="text-gray-400">Please return later to make your offerings.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-950 text-white">
            <div className="max-w-7xl mx-auto px-4 py-6">
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                    <div className="xl:col-span-2 space-y-6">
                        <DailyGoal shrineGoal={shrineGoal} userDonationStatus={userDonationStatus} />
                        <DonationForm
                            shrineMinDonation={SHRINE_MINIMUM_DONATION || 100}
                            currentUser={currentUser}
                            userDonationStatus={userDonationStatus}
                        />
                    </div>
                    <div className="xl:col-span-1">
                        <DonationTable recentDonations={recentDonations} />
                    </div>
                </div>
            </div>
        </div>
    );
}

// Helper function to format buff values for display
const formatBuffValue = (buffType: string, value: number) => {
    if (buffType === "jail" || buffType === "mission" || buffType === "craftSpeed" || buffType === "auctionFees") {
        // These are reduction buffs (multipliers < 1)
        const reduction = Math.round((1 - value) * 100);
        return `${reduction}% reduction`;
    } else {
        // These are increase buffs (multipliers > 1)
        const increase = Math.round((value - 1) * 100);
        return `${increase}% increase`;
    }
};

// Component to display the daily goal progress and buffs
const DailyGoal = ({ shrineGoal, userDonationStatus }: DailyGoalProps) => {
    if (!shrineGoal) return null;
    const goalReached = shrineGoal?.goalReached || shrineGoal?.donationAmount >= shrineGoal?.donationGoal;
    const midnightDate = getNextMidnightDate();
    const buffRewardsArray = Object.values(shrineGoal?.buffRewards || {});
    const progressPercentage = shrineGoal?.donationGoal
        ? Math.min(((shrineGoal.donationAmount || 0) / shrineGoal.donationGoal) * 100, 100)
        : 0;

    return (
        <section className="bg-gray-900/50 border border-gray-800 rounded-xl p-4 md:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                <div className="flex items-center gap-3">
                    <div className="p-2 bg-violet-600 rounded-lg">
                        <TrendingUp className="w-5 h-5 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-white">Daily Goal</h2>
                </div>

                <div className="bg-gray-900 rounded-lg px-3 py-2 border border-gray-800">
                    <div className="flex items-center gap-2 text-gray-400 text-xs">
                        <Clock className="w-3 h-3" />
                        <span>Resets in</span>
                    </div>
                    <div className="text-violet-400 font-bold text-sm">
                        <CountdownTimer showHours targetDate={midnightDate} showSeconds={false} />
                    </div>
                </div>
            </div>

            {/* Goal Status */}
            <div className="text-center mb-4">
                <div
                    className={cn(
                        "inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-semibold mb-3",
                        goalReached
                            ? "bg-green-900/50 text-green-400 border border-green-800"
                            : "bg-gray-800 text-gray-400 border border-gray-700"
                    )}
                >
                    {goalReached ? (
                        <>
                            <Star className="w-3 h-3" />
                            Daily Goal Complete!
                        </>
                    ) : (
                        <>
                            <Sparkles className="w-3 h-3" />
                            Goal In Progress
                        </>
                    )}
                </div>

                <div className="space-y-1">
                    <p className="text-2xl font-bold text-white">{formatCurrency(shrineGoal?.donationAmount || 0)}</p>
                    <p className="text-gray-500 text-sm">of {formatCurrency(shrineGoal?.donationGoal || 0)} goal</p>
                </div>
            </div>

            {/* Progress Bar */}
            <div className="relative mb-4">
                <div className="absolute -top-2 right-0 bg-gray-800 text-gray-300 text-xs px-2 py-0.5 rounded-full">
                    {Math.round(progressPercentage)}%
                </div>
                <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
                    <div
                        className="h-full bg-gradient-to-r from-violet-600 to-violet-500 transition-all duration-500 ease-out"
                        style={{ width: `${progressPercentage}%` }}
                    />
                </div>
            </div>

            {/* Buffs Section */}
            <div>
                <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-gray-300 flex items-center gap-2">
                        <Gift className={cn("w-4 h-4", goalReached ? "text-green-400" : "text-gray-600")} />
                        Today&apos;s Blessings
                    </h3>
                    {userDonationStatus && (
                        <div className="flex items-center gap-1 text-xs">
                            {userDonationStatus.hasMinimumDonation ? (
                                <>
                                    <CheckCircle className="w-3 h-3 text-green-400" />
                                    <span className="text-green-400">Eligible for all buffs</span>
                                </>
                            ) : (
                                <>
                                    <Lock className="w-3 h-3 text-amber-400" />
                                    <span className="text-amber-400">
                                        {formatCurrency(
                                            userDonationStatus.minimumRequired - userDonationStatus.donationAmount
                                        )}{" "}
                                        more for secondary buffs
                                    </span>
                                </>
                            )}
                        </div>
                    )}
                </div>

                <div className="grid grid-cols-1 gap-2">
                    {buffRewardsArray.map((buff, i) => {
                        const isPrimary = buff.isPrimary;
                        const isUserEligible = goalReached && (isPrimary || userDonationStatus?.hasMinimumDonation);
                        const buffValueText = formatBuffValue(buff.buffType, buff.value);

                        return (
                            <div
                                key={i}
                                className={cn(
                                    "flex items-center gap-3 p-3 rounded-lg text-sm transition-all border",
                                    isUserEligible
                                        ? "bg-green-900/20 border-green-800/50 text-green-400"
                                        : goalReached && !isPrimary && !userDonationStatus?.hasMinimumDonation
                                          ? "bg-amber-900/20 border-amber-800/50 text-amber-400"
                                          : "bg-gray-800/50 border-gray-700/50 text-gray-500"
                                )}
                            >
                                <div>
                                    {isUserEligible ? (
                                        <CheckCircle className="w-4 h-4 text-green-400" />
                                    ) : goalReached && !isPrimary && !userDonationStatus?.hasMinimumDonation ? (
                                        <Lock className="w-4 h-4 text-amber-400" />
                                    ) : (
                                        <div
                                            className={cn(
                                                "w-4 h-4 rounded-full border-2",
                                                goalReached ? "border-green-400" : "border-gray-600"
                                            )}
                                        />
                                    )}
                                </div>

                                <div className="flex-1">
                                    <div className="flex items-center gap-2">
                                        <span className="font-medium">{buff.description}</span>
                                        <span
                                            className={cn(
                                                "px-2 py-0.5 rounded-full text-xs font-semibold",
                                                isPrimary
                                                    ? "bg-blue-900/50 text-blue-400 border border-blue-800"
                                                    : "bg-purple-900/50 text-purple-400 border border-purple-800"
                                            )}
                                        >
                                            {isPrimary ? "Global" : "Personal"}
                                        </span>
                                    </div>
                                    <div className="text-xs mt-1 opacity-75">
                                        {buffValueText}
                                        {!isUserEligible && !isPrimary && goalReached && (
                                            <span className="ml-2 text-amber-400">• Requires minimum donation</span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </section>
    );
};

// Component for the donation form
const DonationForm = ({ shrineMinDonation, currentUser, userDonationStatus }: DonationFormProps) => {
    const [donationAmount, setDonationAmount] = useState<string>("");

    const { mutate: postDonation, isPending } = useDonateToShrine(() => {
        setDonationAmount("");
    });

    const handleShrineDonation = (e: FormEvent<HTMLFormElement>): void => {
        e.preventDefault();
        const amount = parseInt(donationAmount, 10);
        if (isNaN(amount) || amount <= 0) {
            toast.error("Please enter a valid donation amount.");
            return;
        }
        if (amount > currentUser?.cash) {
            toast.error("You don't have enough cash to donate!");
            return;
        }
        if (amount < shrineMinDonation) {
            toast.error(`Minimum donation is ${formatCurrency(shrineMinDonation)}.`);
            return;
        }
        postDonation({ amount });
    };

    return (
        <section className="bg-gray-900/50 border border-gray-800 rounded-xl p-4 md:p-6">
            <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-amber-600 rounded-lg">
                    <Coins className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-white">Make an Offering</h2>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-3 mb-4 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400 text-sm">Your Balance</span>
                    <span className="text-lg font-bold text-white">{formatCurrency(currentUser?.cash || 0)}</span>
                </div>

                {userDonationStatus && (
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-400 text-sm">Today's Donation</span>
                        <span className="text-sm font-semibold text-white">
                            {formatCurrency(userDonationStatus.donationAmount)}
                        </span>
                    </div>
                )}

                <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Minimum offering: {formatCurrency(shrineMinDonation)}</span>
                    {userDonationStatus && !userDonationStatus.hasMinimumDonation && (
                        <span className="text-amber-400 font-medium">
                            {formatCurrency(userDonationStatus.minimumRequired - userDonationStatus.donationAmount)}{" "}
                            needed for personal buffs
                        </span>
                    )}
                </div>
            </div>

            <form className="space-y-3" onSubmit={handleShrineDonation}>
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <span className="text-gray-500 font-semibold">{getCurrencySymbol("yen")}</span>
                    </div>
                    <input
                        type="number"
                        name="amount"
                        id="amount"
                        min={shrineMinDonation}
                        className="block w-full rounded-lg border border-gray-700 bg-gray-800 pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:border-violet-500 focus:ring-1 focus:ring-violet-500/20 focus:outline-none transition-all"
                        placeholder={`Enter amount (min ${shrineMinDonation})`}
                        value={donationAmount}
                        disabled={isPending}
                        onChange={(e) => setDonationAmount(e.target.value)}
                    />
                </div>

                <button
                    type="submit"
                    disabled={isPending}
                    className={cn(
                        "w-full h-10 px-4 rounded-lg font-medium transition-all duration-200",
                        "flex items-center justify-center gap-2",
                        isPending
                            ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                            : "bg-violet-600 hover:bg-violet-700 text-white"
                    )}
                >
                    {isPending ? (
                        <>
                            <div className="w-4 h-4 border-2 border-gray-500 border-t-white rounded-full animate-spin" />
                            <span>Offering...</span>
                        </>
                    ) : (
                        <>
                            <Heart className="w-4 h-4" />
                            <span>Make Offering</span>
                        </>
                    )}
                </button>
            </form>
        </section>
    );
};

// Component for the donation history table
const DonationTable = ({ recentDonations }: DonationTableProps) => {
    return (
        <aside className="bg-gray-900/50 border border-gray-800 rounded-xl p-4 md:p-6 h-full">
            <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-violet-600 rounded-lg">
                    <Users className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-white">Recent Offerings</h2>
            </div>

            <div className="space-y-2">
                {recentDonations?.map((donation, index) => (
                    <div
                        key={donation.id}
                        className="bg-gray-800/50 rounded-lg p-3 border border-gray-700 hover:bg-gray-800/70 transition-all duration-200"
                    >
                        <div className="flex items-center gap-3">
                            <div className="relative">
                                <DisplayAvatar
                                    className="h-10 w-10 rounded-full border border-gray-700"
                                    src={donation?.user}
                                />
                                {index === 0 && (
                                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-amber-600 rounded-full flex items-center justify-center">
                                        <Star className="w-2.5 h-2.5 text-white" />
                                    </div>
                                )}
                            </div>

                            <div className="flex-1 min-w-0">
                                <Link
                                    to={`/profile/${donation?.userId}`}
                                    className="font-medium text-white hover:text-violet-400 transition-colors block truncate text-sm"
                                >
                                    {donation?.user.username}
                                </Link>
                                <div className="flex items-center gap-2 text-xs">
                                    <span className="text-amber-400 font-bold">
                                        {formatCurrency(donation?.amount || 0)}
                                    </span>
                                    <span className="text-gray-600">•</span>
                                    <span className="text-gray-500">{formatTimeToNow(donation?.createdAt)} ago</span>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}

                {(!recentDonations || recentDonations.length === 0) && (
                    <div className="text-center py-8 text-gray-500">
                        <Heart className="w-10 h-10 mx-auto mb-3 text-gray-700" />
                        <p className="text-sm">No recent offerings yet</p>
                        <p className="text-xs mt-1">Be the first to contribute!</p>
                    </div>
                )}
            </div>
        </aside>
    );
};

export default Shrine;
